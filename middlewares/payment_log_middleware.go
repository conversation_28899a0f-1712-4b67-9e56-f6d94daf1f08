/**
 * payment_log_middleware.go
 * 支付日志中间件
 *
 * 本文件实现了支付相关操作日志记录的中间件功能，自动记录用户的支付敏感操作。
 * 包括支付创建、查询、关闭、退款等操作的详细日志记录，用于安全审计和问题追踪。
 */

package middlewares

import (
	"strings"

	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web/context"

	userDto "o_mall_backend/modules/user/dto"
	"o_mall_backend/modules/user/models"
	"o_mall_backend/modules/user/services"
	"o_mall_backend/utils"
)

// 需要记录日志的支付操作路径
var sensitivePaymentPaths = []string{
	"/api/v1/user/payments/create",     // 创建支付
	"/api/v1/user/payments/query/",     // 查询支付
	"/api/v1/user/payments/close/",     // 关闭支付
	"/api/v1/user/refunds/create",      // 创建退款
	"/api/v1/user/refunds/query/",      // 查询退款
}

// PaymentLogMiddleware 支付日志中间件
// 自动记录用户支付相关敏感操作日志
func PaymentLogMiddleware() func(ctx *context.Context) {
	return func(ctx *context.Context) {
		path := ctx.Request.URL.Path
		method := ctx.Request.Method

		// 检查是否是需要记录的支付操作
		if !isPaymentSensitiveOperation(path, method) {
			return
		}

		logs.Info("====== 支付日志中间件触发 ======")
		logs.Info("请求路径: %s, 方法: %s", path, method)

		// 获取用户ID
		userID := getUserIDFromContext(ctx)
		if userID <= 0 {
			logs.Warn("未获取到有效的用户ID，不记录支付日志")
			return
		}

		// 获取用户名
		username := getUsernameFromContext(ctx)

		// 确定操作类型和内容
		operationType, content := getPaymentOperationInfo(path, method)
		logs.Info("支付操作类型: %d, 内容: %s", operationType, content)

		// 准备日志数据
		logReq := &userDto.UserLogCreateRequest{
			UserID:       userID,
			Username:     username,
			OperationType: operationType,
			Content:      content,
			RequestURL:   path,
			RequestData:  string(ctx.Input.RequestBody),
			IP:           ctx.Input.IP(),
			UserAgent:    ctx.Input.UserAgent(),
			Status:       1, // 默认成功状态
			Remark:       "支付操作成功",
		}

		// 异步记录日志
		go func(req *userDto.UserLogCreateRequest) {
			logService := services.NewUserLogService()
			result, err := logService.CreateLog(utils.CreateContext(), req)
			if err != nil {
				logs.Error("记录支付操作日志失败: %v", err)
			} else {
				logs.Info("成功记录支付操作日志，日志ID: %d", result)
			}
		}(logReq)

		logs.Info("====== 支付日志中间件处理完成 ======")
	}
}

// isPaymentSensitiveOperation 判断是否是需要记录日志的支付敏感操作
func isPaymentSensitiveOperation(path, method string) bool {
	// 检查是否匹配支付敏感路径
	for _, sensitivePath := range sensitivePaymentPaths {
		if strings.HasPrefix(path, sensitivePath) {
			return true
		}
	}
	
	return false
}

// getPaymentOperationInfo 根据路径和方法获取支付操作信息
func getPaymentOperationInfo(path, method string) (int, string) {
	// 根据路径确定操作类型和内容
	switch {
	case strings.Contains(path, "/payments/create"):
		return models.UserLogTypeConsume, "创建支付订单"
	case strings.Contains(path, "/payments/query/"):
		return models.UserLogTypeConsume, "查询支付状态"
	case strings.Contains(path, "/payments/close/"):
		return models.UserLogTypeConsume, "关闭支付订单"
	case strings.Contains(path, "/refunds/create"):
		return models.UserLogTypeConsume, "创建退款申请"
	case strings.Contains(path, "/refunds/query/"):
		return models.UserLogTypeConsume, "查询退款状态"
	default:
		return models.UserLogTypeConsume, "支付相关操作"
	}
}
