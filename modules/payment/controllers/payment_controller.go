/**
 * payment_controller.go
 * 支付控制器
 *
 * 该文件实现了支付相关的API接口控制器，处理支付创建、查询、回调等HTTP请求。
 * 控制器负责参数校验、调用服务层处理业务逻辑，以及返回处理结果。
 */

package controllers

import (
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/core/validation"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/common/auth"
	"o_mall_backend/common/result"
	paymentDto "o_mall_backend/modules/payment/dto"
	"o_mall_backend/modules/payment/services"
	"o_mall_backend/utils/common"
)

// PaymentController 支付控制器
type PaymentController struct {
	web.Controller
	paymentService services.PaymentService
	refundService  services.RefundService
}

// Prepare 初始化控制器
// 在每个Action执行前调用，用于初始化服务实例
func (c *PaymentController) Prepare() {
	c.paymentService = services.NewPaymentService()
	c.refundService = services.NewRefundService()
}

// ParseRequest 解析请求体到结构体
// 支持JSON和表单数据格式
func (c *PaymentController) ParseRequest(req interface{}) error {
	return common.ParseRequest(c.Ctx, req)
}

// CreatePayment 创建支付
// @Title 创建支付
// @Description 创建新的支付记录并返回支付参数
// @Param	body	body	paymentDto.PaymentCreateRequest	true	"支付创建请求"
// @Success 200 {object} dto.Response 成功返回支付参数
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /create [post]
func (c *PaymentController) CreatePayment() {
	// 获取当前用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	logs.Info("获取到用户ID: %d", userID)

	// 解析请求体
	var req paymentDto.PaymentCreateRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	if ok, _ := valid.Valid(&req); !ok {
		result.HandleError(c.Ctx, result.ErrInvalidParams, valid.Errors[0].Message)
		return
	}

	// 调用服务创建支付
	resp, err := c.paymentService.CreatePayment(&req)
	if err != nil {
		logs.Error("创建支付失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// QueryPayment 查询支付状态
// @Title 查询支付状态
// @Description 查询支付记录状态
// @Param	transaction_no	path	string	true	"交易流水号"
// @Success 200 {object} dto.Response 成功返回支付状态
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /query/:transaction_no [get]
func (c *PaymentController) QueryPayment() {
	// 获取当前用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	logs.Info("查询支付 - 获取到用户ID: %d", userID)

	// 获取交易流水号
	transactionNo := c.Ctx.Input.Param(":transaction_no")
	if transactionNo == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "交易流水号不能为空")
		return
	}

	// 构建查询请求
	req := &paymentDto.PaymentQueryRequest{
		TransactionNo: transactionNo,
	}

	// 调用服务查询支付
	resp, err := c.paymentService.QueryPayment(req)
	if err != nil {
		logs.Error("查询支付失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// ClosePayment 关闭支付
// @Title 关闭支付
// @Description 关闭未支付的支付记录
// @Param	transaction_no	path	string	true	"交易流水号"
// @Success 200 {object} dto.Response 成功返回关闭结果
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /close/:transaction_no [post]
func (c *PaymentController) ClosePayment() {
	// 获取当前用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	logs.Info("关闭支付 - 获取到用户ID: %d", userID)

	// 获取交易流水号
	transactionNo := c.Ctx.Input.Param(":transaction_no")
	if transactionNo == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "交易流水号不能为空")
		return
	}

	// 解析请求体
	var req paymentDto.PaymentCancelRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的请求参数")
		return
	}

	// 设置交易流水号
	req.TransactionNo = transactionNo

	// 调用服务关闭支付
	success, err := c.paymentService.CancelPayment(&req)
	if err != nil {
		logs.Error("关闭支付失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, map[string]bool{"success": success})
}

// PaymentCallback 支付回调处理
// @Title 支付回调处理
// @Description 处理支付平台的回调通知
// @Param	method	path	string	true	"支付方式"
// @Param	order_no	path	string	true	"订单号"
// @Success 200 {string} string 成功返回success
// @Failure 400 {string} string 参数错误
// @Failure 500 {string} string 服务器内部错误
// @router /callback/:method/:order_no [post]
func (c *PaymentController) PaymentCallback() {
	// 获取支付方式和订单号
	method := c.Ctx.Input.Param(":method")
	orderNo := c.Ctx.Input.Param(":order_no")

	if method == "" || orderNo == "" {
		c.Ctx.WriteString("fail")
		return
	}

	// 获取原始回调数据
	rawData := string(c.Ctx.Input.RequestBody)

	// 构建回调请求
	req := &paymentDto.PaymentCallbackRequest{
		RawData: rawData,
	}

	// 根据支付方式设置Method
	switch method {
	case "wechat":
		req.Method = 1 // 假设1代表微信支付
	case "alipay":
		req.Method = 2 // 假设2代表支付宝
	case "balance":
		req.Method = 3 // 假设3代表余额支付
	default:
		c.Ctx.WriteString("fail")
		return
	}

	// 调用服务处理回调
	success, err := c.paymentService.HandlePaymentCallback(req)
	if err != nil {
		logs.Error("处理支付回调失败: %v", err)
		c.Ctx.WriteString("fail")
		return
	}

	// 返回处理结果
	if success {
		c.Ctx.WriteString("success")
	} else {
		c.Ctx.WriteString("fail")
	}
}

// CreateRefund 创建退款
// @Title 创建退款
// @Description 创建新的退款记录
// @Param	body	body	paymentDto.RefundCreateRequest	true	"退款创建请求"
// @Success 200 {object} dto.Response 成功返回退款参数
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /refunds/create [post]
func (c *PaymentController) CreateRefund() {
	// 获取当前用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	logs.Info("创建退款 - 获取到用户ID: %d", userID)

	// 解析请求体
	var req paymentDto.RefundCreateRequest
	if err := c.ParseRequest(&req); err != nil {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "无效的请求参数")
		return
	}

	// 参数校验
	valid := validation.Validation{}
	if ok, _ := valid.Valid(&req); !ok {
		result.HandleError(c.Ctx, result.ErrInvalidParams, valid.Errors[0].Message)
		return
	}

	// 调用服务创建退款
	resp, err := c.refundService.CreateRefund(&req)
	if err != nil {
		logs.Error("创建退款失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// QueryRefund 查询退款状态
// @Title 查询退款状态
// @Description 查询退款记录状态
// @Param	refund_no	path	string	true	"退款流水号"
// @Success 200 {object} dto.Response 成功返回退款状态
// @Failure 400 {object} dto.Response 参数错误
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /refunds/query/:refund_no [get]
func (c *PaymentController) QueryRefund() {
	// 获取当前用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	logs.Info("查询退款 - 获取到用户ID: %d", userID)

	// 获取退款流水号
	refundNo := c.Ctx.Input.Param(":refund_no")
	if refundNo == "" {
		result.HandleError(c.Ctx, result.ErrInvalidParams, "退款流水号不能为空")
		return
	}

	// 构建查询请求
	req := &paymentDto.RefundQueryRequest{
		RefundNo: refundNo,
	}

	// 调用服务查询退款
	resp, err := c.refundService.QueryRefund(req)
	if err != nil {
		logs.Error("查询退款失败: %v", err)
		result.HandleError(c.Ctx, err)
		return
	}

	// 返回成功响应
	result.OK(c.Ctx, resp)
}

// RefundCallback 退款回调处理
// @Title 退款回调处理
// @Description 处理退款平台的回调通知
// @Param	method	path	string	true	"支付方式"
// @Param	order_no	path	string	true	"订单号"
// @Success 200 {string} string 成功返回success
// @Failure 400 {string} string 参数错误
// @Failure 500 {string} string 服务器内部错误
// @router /refund/callback/:method/:order_no [post]
func (c *PaymentController) RefundCallback() {
	// 获取支付方式和订单号
	method := c.Ctx.Input.Param(":method")
	orderNo := c.Ctx.Input.Param(":order_no")

	if method == "" || orderNo == "" {
		c.Ctx.WriteString("fail")
		return
	}

	// 获取原始回调数据
	rawData := string(c.Ctx.Input.RequestBody)

	// 获取退款流水号（实际项目中可能需要从请求中解析）
	refundNo := ""
	// 获取外部退款单号（实际项目中可能需要从请求中解析）
	externalNo := ""

	// 调用服务处理回调
	success, err := c.refundService.HandleRefundCallback(refundNo, externalNo, rawData)
	if err != nil {
		logs.Error("处理退款回调失败: %v", err)
		c.Ctx.WriteString("fail")
		return
	}

	// 返回处理结果
	if success {
		c.Ctx.WriteString("success")
	} else {
		c.Ctx.WriteString("fail")
	}
}

// GetPaymentMethods 获取支付方式列表
// @Title 获取支付方式列表
// @Description 获取系统支持的支付方式列表
// @Success 200 {object} dto.Response 成功返回支付方式列表
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /methods [get]
func (c *PaymentController) GetPaymentMethods() {
	// 获取当前用户ID（如果需要的话）
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		// 支付方式列表可以不需要用户认证，提供默认列表
		logs.Warn("获取用户ID失败，返回默认支付方式列表: %v", err)
	} else {
		logs.Info("获取支付方式列表 - 用户ID: %d", userID)
	}

	// 构造支付方式列表
	methods := []map[string]interface{}{
		{
			"method":      "wechat",
			"name":        "微信支付",
			"icon":        "/static/icons/wechat-pay.png",
			"enabled":     true,
			"description": "微信安全支付",
			"minAmount":   0.01,
			"maxAmount":   50000.0,
			"feeRate":     0.0,
		},
		{
			"method":      "alipay",
			"name":        "支付宝",
			"icon":        "/static/icons/alipay.png",
			"enabled":     true,
			"description": "支付宝安全支付",
			"minAmount":   0.01,
			"maxAmount":   50000.0,
			"feeRate":     0.0,
		},
		{
			"method":      "balance",
			"name":        "余额支付",
			"icon":        "/static/icons/balance.png",
			"enabled":     true,
			"description": "使用账户余额支付",
			"minAmount":   0.01,
			"maxAmount":   10000.0,
			"feeRate":     0.0,
		},
	}

	result.OK(c.Ctx, map[string]interface{}{
		"methods": methods,
	})
}

// GetUserBalance 获取用户余额
// @Title 获取用户余额
// @Description 获取当前用户的账户余额信息
// @Success 200 {object} dto.Response 成功返回用户余额
// @Failure 401 {object} dto.Response 未授权
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /balance [get]
func (c *PaymentController) GetUserBalance() {
	// 获取当前用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	logs.Info("获取用户余额 - 用户ID: %d", userID)

	// TODO: 这里应该从数据库获取真实的用户余额
	// 目前返回模拟数据
	balance := map[string]interface{}{
		"balance":          1000.00,
		"frozenBalance":    0.00,
		"availableBalance": 1000.00,
		"currency":         "CNY",
	}

	result.OK(c.Ctx, balance)
}

// GetBankCards 获取用户银行卡列表
// @Title 获取用户银行卡列表
// @Description 获取当前用户绑定的银行卡列表
// @Success 200 {object} dto.Response 成功返回银行卡列表
// @Failure 401 {object} dto.Response 未授权
// @Failure 500 {object} dto.Response 服务器内部错误
// @router /bank-cards [get]
func (c *PaymentController) GetBankCards() {
	// 获取当前用户ID
	userID, err := auth.GetUserIDFromContext(c.Ctx)
	if err != nil {
		result.HandleError(c.Ctx, err)
		return
	}
	logs.Info("获取银行卡列表 - 用户ID: %d", userID)

	// TODO: 这里应该从数据库获取真实的银行卡列表
	// 目前返回模拟数据
	bankCards := []map[string]interface{}{
		{
			"id":         "1",
			"cardNo":     "****************",
			"bankName":   "工商银行",
			"cardType":   "储蓄卡",
			"holderName": "张三",
			"isDefault":  true,
			"createdAt":  "2024-01-01 10:00:00",
		},
		{
			"id":         "2",
			"cardNo":     "****************",
			"bankName":   "农业银行",
			"cardType":   "储蓄卡",
			"holderName": "张三",
			"isDefault":  false,
			"createdAt":  "2024-01-02 10:00:00",
		},
	}

	result.OK(c.Ctx, bankCards)
}
