/**
 * payment模块路由
 *
 * 本文件负责注册payment模块的所有路由，将请求映射到对应的控制器处理函数。
 * 支付相关接口需要用户认证，使用JWT中间件进行权限控制。
 */

package routers

import (
	"github.com/beego/beego/v2/core/logs"
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/middlewares"
	"o_mall_backend/modules/payment/controllers"
)

// InitRouters 初始化支付模块路由
func InitRouters() {
	logs.Info("初始化支付模块路由...")

	// 配置支付模块路由
	configurePaymentRoutes()

	logs.Info("支付模块路由初始化完成")
}

// configurePaymentRoutes 配置支付模块路由
func configurePaymentRoutes() {
	logs.Info("配置支付模块路由...")

	// 用户支付API前缀 - 需要用户认证
	userPaymentNs := web.NewNamespace("/api/v1/payment",
		web.NSNamespace("/payments",
			// 添加JWT认证中间件
			web.NSBefore(middlewares.JWTFilter),
			// 添加支付日志中间件
			web.NSBefore(middlewares.PaymentLogMiddleware()),

			// 支付相关路由
			web.NSRouter("/create", &controllers.PaymentController{}, "post:CreatePayment"),
			web.NSRouter("/query/:transaction_no", &controllers.PaymentController{}, "get:QueryPayment"),
			web.NSRouter("/close/:transaction_no", &controllers.PaymentController{}, "post:ClosePayment"),
		),

		web.NSNamespace("/refunds",
			// 添加JWT认证中间件
			web.NSBefore(middlewares.JWTFilter),
			// 添加支付日志中间件
			web.NSBefore(middlewares.PaymentLogMiddleware()),

			// 退款相关路由
			web.NSRouter("/create", &controllers.PaymentController{}, "post:CreateRefund"),
			web.NSRouter("/query/:refund_no", &controllers.PaymentController{}, "get:QueryRefund"),
		),
	)

	// 支付辅助接口 - 部分需要认证，部分无需认证
	paymentHelperNs := web.NewNamespace("/api/v1",
		// 支付方式列表 - 无需认证
		web.NSRouter("/payment/methods", &controllers.PaymentController{}, "get:GetPaymentMethods"),

		web.NSNamespace("/userpay",
			// 添加JWT认证中间件
			web.NSBefore(middlewares.JWTFilter),

			// 用户余额 - 需要认证
			web.NSRouter("/payment/balance", &controllers.PaymentController{}, "get:GetUserBalance"),
			// 银行卡列表 - 需要认证
			web.NSRouter("/payment/bank-cards", &controllers.PaymentController{}, "get:GetBankCards"),
		),
	)

	// 支付回调路由 - 无需认证（第三方平台回调）
	callbackNs := web.NewNamespace("/api",
		// 支付回调路由
		web.NSRouter("/payment/callback/:method/:order_no", &controllers.PaymentController{}, "post:PaymentCallback"),
		// 退款回调路由
		web.NSRouter("/refund/callback/:method/:order_no", &controllers.PaymentController{}, "post:RefundCallback"),
	)

	// 注册命名空间
	web.AddNamespace(userPaymentNs)
	web.AddNamespace(paymentHelperNs)
	web.AddNamespace(callbackNs)

	logs.Info("支付模块路由配置完成")
}
