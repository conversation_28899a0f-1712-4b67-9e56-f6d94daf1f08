# 外卖订单余额支付功能测试

## 测试环境准备

### 1. 数据库准备

确保以下数据存在：

```sql
-- 创建测试用户
INSERT INTO user (id, username, phone, password, status, created_at, updated_at) 
VALUES (1, 'testuser', '***********', 'hashed_password', 1, NOW(), NOW());

-- 创建用户账户（余额100元）
INSERT INTO user_account (user_id, balance, total_recharge, total_consume, frozen_balance, created_at, updated_at)
VALUES (1, 100.00, 100.00, 0.00, 0.00, NOW(), NOW());

-- 创建测试商家
INSERT INTO merchant (id, name, status, created_at, updated_at)
VALUES (1, '测试餐厅', 1, NOW(), NOW());

-- 创建测试商品
INSERT INTO takeout_food (id, merchant_id, name, price, status, created_at, updated_at)
VALUES (1, 1, '宫保鸡丁', 28.00, 1, NOW(), NOW());

-- 创建购物车项
INSERT INTO takeout_cart (id, user_id, merchant_id, food_id, quantity, price, created_at, updated_at)
VALUES (1, 1, 1, 1, 2, 28.00, NOW(), NOW());

-- 创建配送地址
INSERT INTO user_address (id, user_id, name, phone, province, city, district, address, is_default, created_at, updated_at)
VALUES (1, 1, '张三', '***********', '北京市', '朝阳区', '望京街道', '望京SOHO T1', 1, NOW(), NOW());
```

### 2. API测试

#### 测试用例1：使用数字类型支付方式（余额支付）

**请求URL**: `POST /api/v1/takeout/orders/create`

**请求Headers**:
```
Content-Type: application/json
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**请求Body**:
```json
{
    "takeoutAddressID": 1,
    "addressLat": 39.9042,
    "addressLng": 116.4074,
    "paymentMethodInt": 3,
    "clientIP": "*************",
    "deviceInfo": "iPhone 13",
    "merchantOrders": [
        {
            "merchantID": 1,
            "cartItemIDs": [1],
            "couponID": 0,
            "promotionID": 0,
            "deliveryTime": "2024-01-15 12:00:00",
            "remark": "不要辣"
        }
    ]
}
```

**期望响应**:
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "orderID": 1,
            "orderNo": "TO202401151200001",
            "merchantID": 1,
            "merchantName": "测试餐厅",
            "totalAmount": 56.00,
            "payAmount": 56.00,
            "status": 20,
            "payStatus": 1,
            "deliveryStatus": 1,
            "paymentMethod": "余额支付"
        }
    ]
}
```

#### 测试用例2：使用字符串类型支付方式

**请求Body**:
```json
{
    "takeoutAddressID": 1,
    "addressLat": 39.9042,
    "addressLng": 116.4074,
    "paymentMethod": "3",
    "clientIP": "*************",
    "deviceInfo": "Web Browser",
    "merchantOrders": [
        {
            "merchantID": 1,
            "cartItemIDs": [1],
            "couponID": 0,
            "promotionID": 0,
            "deliveryTime": "2024-01-15 12:30:00",
            "remark": "多放点花生米"
        }
    ]
}
```

#### 测试用例3：使用描述性文本支付方式

**请求Body**:
```json
{
    "takeoutAddressID": 1,
    "addressLat": 39.9042,
    "addressLng": 116.4074,
    "paymentMethod": "余额支付",
    "clientIP": "*************",
    "deviceInfo": "Android App",
    "merchantOrders": [
        {
            "merchantID": 1,
            "cartItemIDs": [1],
            "couponID": 0,
            "promotionID": 0,
            "deliveryTime": "2024-01-15 13:00:00",
            "remark": "少盐少油"
        }
    ]
}
```

#### 测试用例4：余额不足的情况

**前置条件**: 将用户余额设置为10元（小于订单金额）

```sql
UPDATE user_account SET balance = 10.00 WHERE user_id = 1;
```

**期望响应**:
```json
{
    "code": 500,
    "message": "余额支付失败: 余额不足",
    "data": null
}
```

#### 测试用例5：非余额支付方式（微信支付）

**请求Body**:
```json
{
    "takeoutAddressID": 1,
    "addressLat": 39.9042,
    "addressLng": 116.4074,
    "paymentMethodInt": 1,
    "clientIP": "*************",
    "deviceInfo": "iPhone 13",
    "merchantOrders": [
        {
            "merchantID": 1,
            "cartItemIDs": [1],
            "couponID": 0,
            "promotionID": 0,
            "deliveryTime": "2024-01-15 12:00:00",
            "remark": "不要辣"
        }
    ]
}
```

**期望行为**: 订单创建成功，但不自动处理支付，订单状态为待支付

## 验证步骤

### 1. 数据库验证

**检查订单状态**:
```sql
SELECT id, order_no, status, pay_status, pay_amount, pay_method 
FROM `order` 
WHERE user_id = 1 
ORDER BY created_at DESC LIMIT 1;
```

**检查用户余额变化**:
```sql
SELECT balance, total_consume 
FROM user_account 
WHERE user_id = 1;
```

**检查交易记录**:
```sql
SELECT * FROM user_account_transaction 
WHERE user_id = 1 
ORDER BY created_at DESC LIMIT 1;
```

**检查支付记录**:
```sql
SELECT * FROM payment 
WHERE user_id = 1 
ORDER BY created_at DESC LIMIT 1;
```

### 2. 日志验证

查看应用日志，确认以下关键日志：

```
[外卖订单控制器] 检测到余额支付，开始处理余额支付流程
[余额支付处理] 开始处理订单余额支付
[余额支付处理器] 开始处理余额支付
[余额支付处理器] 用户余额扣减成功
[余额支付处理] 订单余额支付处理完成
```

## 测试工具

### 使用curl测试

```bash
curl -X POST http://localhost:8080/api/v1/takeout/orders/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "takeoutAddressID": 1,
    "addressLat": 39.9042,
    "addressLng": 116.4074,
    "paymentMethodInt": 3,
    "clientIP": "*************",
    "deviceInfo": "curl",
    "merchantOrders": [
      {
        "merchantID": 1,
        "cartItemIDs": [1],
        "couponID": 0,
        "promotionID": 0,
        "deliveryTime": "2024-01-15 12:00:00",
        "remark": "测试订单"
      }
    ]
  }'
```

### 使用Postman测试

1. 创建新的POST请求
2. 设置URL为 `http://localhost:8080/api/v1/takeout/orders/create`
3. 在Headers中添加 `Content-Type: application/json` 和 `Authorization: Bearer YOUR_ACCESS_TOKEN`
4. 在Body中选择raw格式，粘贴测试JSON数据
5. 发送请求并检查响应

## 预期结果

1. **余额支付成功**：订单创建成功，状态为已支付，用户余额相应扣减
2. **余额不足**：返回错误信息，订单不会创建
3. **非余额支付**：订单创建成功，状态为待支付，不扣减余额
4. **日志完整**：所有关键步骤都有相应的日志记录
5. **数据一致性**：订单、支付、账户数据保持一致

## 重要修复说明

### 支付方式常量映射问题

在测试过程中发现了支付模块和订单模块的支付方式常量定义不一致的问题：

- **订单模块**：余额支付 = 3
- **支付模块**：余额支付 = 5，信用卡 = 3

**修复方案**：在外卖订单服务中增加了支付方式转换逻辑，确保正确映射。

### 修复后的期望日志

```
[支付创建流程] 步骤5: 检查支付方式 - 支付方式: 5 (余额支付)
[余额支付处理器] 开始处理余额支付
[余额支付处理器] 用户余额扣减成功
```

### 如果遇到错误

如果看到以下错误日志：
```
[E] 不支持的支付方式: 信用卡
```

说明支付方式映射有问题，请检查代码中的转换逻辑是否正确实现。
