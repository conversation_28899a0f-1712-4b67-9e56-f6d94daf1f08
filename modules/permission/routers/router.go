/**
 * 权限路由配置
 *
 * 该文件配置了权限相关的路由。
 * 包括权限管理、角色管理、权限分配等功能的路由。
 */

package routers

import (
	"github.com/beego/beego/v2/server/web"

	"o_mall_backend/middlewares"
	"o_mall_backend/modules/permission/controllers"
)

// InitPermissionRouter 初始化权限路由
func InitPermissionRouter() {
	// 权限管理（需要管理员认证）
	// web.Router("/api/v1/admin/secured/permissions", &controllers.PermissionController{}, "get:ListPermissions")
	// web.Router("/api/v1/admin/secured/permissions/:id", &controllers.PermissionController{}, "get:GetPermission")
	// web.Router("/api/v1/admin/secured/permissions", &controllers.PermissionController{}, "post:CreatePermission")
	// web.Router("/api/v1/admin/secured/permissions/:id", &controllers.PermissionController{}, "put:UpdatePermission")
	// web.Router("/api/v1/admin/secured/permissions/:id", &controllers.PermissionController{}, "delete:DeletePermission")

	// // 角色管理（需要管理员认证）
	// web.Router("/api/v1/admin/secured/roles", &controllers.PermissionController{}, "get:ListRoles")
	// web.Router("/api/v1/admin/secured/roles/:id", &controllers.PermissionController{}, "get:GetRole")
	// web.Router("/api/v1/admin/secured/roles", &controllers.PermissionController{}, "post:CreateRole")
	// web.Router("/api/v1/admin/secured/roles/:id", &controllers.PermissionController{}, "put:UpdateRole")
	// web.Router("/api/v1/admin/secured/roles/:id", &controllers.PermissionController{}, "delete:DeleteRole")

	// // 角色分配（需要管理员认证）
	// web.Router("/api/v1/admin/secured/roles/assign", &controllers.PermissionController{}, "post:AssignRole")
	// web.Router("/api/v1/admin/secured/roles/:id/permissions", &controllers.PermissionController{}, "put:AssignPermissions")

	// // 用户权限（需要管理员认证）
	// web.Router("/api/v1/admin/secured/users/:user_id/permissions", &controllers.PermissionController{}, "get:GetUserPermissions")
	// web.Router("/api/v1/admin/secured/users/:user_id/roles", &controllers.PermissionController{}, "get:GetUserRoles")

	// 当前用户权限（需要用户认证）
	// 修复：将路径改为/api/v1/user/secured，避免与登录接口冲突
	userNS := web.NewNamespace("/api/v1/user/secured",
		// 添加JWT中间件
		web.NSBefore(middlewares.AuthMiddleware),

		// 当前用户权限相关
		web.NSRouter("/permissions", &controllers.PermissionController{}, "get:GetCurrentUserPermissions"),
		web.NSRouter("/roles", &controllers.PermissionController{}, "get:GetCurrentUserRoles"),
	)

	// // 注册命名空间
	web.AddNamespace(userNS)
}
